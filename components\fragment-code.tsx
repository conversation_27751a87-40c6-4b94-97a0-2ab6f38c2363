'use client'

import { useState, useEffect } from 'react'
import { FileExplorer } from './file-explorer'
import { CodeEditor } from './code-editor'
import { cn } from '@/lib/utils'

interface File {
  name: string
  content: string
  type?: string
}

interface FragmentCodeProps {
  files: File[]
}

export function FragmentCode({ files }: FragmentCodeProps) {
  const [selectedFile, setSelectedFile] = useState<string | null>(
    files.length > 0 ? files[0].name : null
  )
  const [explorerWidth, setExplorerWidth] = useState(300) // Default 300px width
  const [isResizing, setIsResizing] = useState(false)

  // Handle file selection
  const handleFileSelect = (fileName: string) => {
    setSelectedFile(fileName)
  }

  // Handle resizing
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsResizing(true)
    e.preventDefault()
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing) return

    const newWidth = e.clientX
    if (newWidth >= 200 && newWidth <= 500) {
      setExplorerWidth(newWidth)
    }
  }

  const handleMouseUp = () => {
    setIsResizing(false)
  }

  // Add event listeners for resizing
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)

      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isResizing])

  if (files.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground">
        <div className="text-center">
          <div className="text-lg font-medium mb-2">No files available</div>
          <div className="text-sm">No code files to display</div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-full bg-background overflow-hidden">
      {/* File Explorer */}
      <div
        className="flex-shrink-0 border-r border-border"
        style={{ width: explorerWidth }}
      >
        <FileExplorer
          files={files}
          selectedFile={selectedFile}
          onFileSelect={handleFileSelect}
        />
      </div>

      {/* Resize handle */}
      <div
        className={cn(
          'w-1 bg-border cursor-col-resize hover:bg-primary/20 transition-colors',
          isResizing && 'bg-primary/30'
        )}
        onMouseDown={handleMouseDown}
      />

      {/* Code Editor */}
      <div className="flex-1 min-w-0">
        <CodeEditor
          files={files}
          selectedFile={selectedFile}
          onFileSelect={handleFileSelect}
        />
      </div>
    </div>
  )
}
