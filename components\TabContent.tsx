"use client"

import { Suspense } from "react"
import dynamic from "next/dynamic"
import { useTab } from "@/contexts/TabContext"
import OrbNav from "@/components/OrbNav"
import { FragmentCode } from "@/components/fragment-code"
import { FragmentPreview } from "@/components/fragment-preview"
import { FragmentSchema } from "@/lib/schema"
import { ExecutionResult } from "@/lib/types"
import { DeepPartial } from "ai"

// Dynamically import components to avoid SSR issues
const Globe = dynamic(() => import("@/components/Globe"), { ssr: false })

interface TabContentProps {
  sandboxId?: string
  fragment?: DeepPartial<FragmentSchema>
  result?: ExecutionResult
  refreshKey?: number
}

export default function TabContent({ sandboxId, fragment, result, refreshKey }: TabContentProps) {
  const { activeTab } = useTab()

  const renderContent = () => {
    switch (activeTab) {
      case 'orb':
        return (
          <div className="relative h-full">
            <Suspense
              fallback={
                <div className="flex items-center justify-center h-full">
                  <div className="text-gray-400">Loading The Orb...</div>
                </div>
              }
            >
              <Globe />
              <OrbNav />
            </Suspense>
          </div>
        )

      case 'preview':
        if (result) {
          return (
            <div className="h-full w-full">
              <FragmentPreview result={result} refreshKey={refreshKey} />
            </div>
          )
        }
        return (
          <div className="flex items-center justify-center h-full">
            <div className="text-gray-400">No preview available</div>
          </div>
        )

      case 'code':
        // Multi-file application support
        if (fragment?.is_multi_file && fragment?.files && fragment.files.length > 0) {
          return (
            <div className="h-full w-full">
              <FragmentCode
                files={fragment.files.map(file => ({
                  name: file.file_path || file.file_name || 'unknown',
                  content: file.file_content || '',
                }))}
              />
            </div>
          )
        }
        // Single-file application (legacy support)
        else if (fragment?.code && fragment?.file_path) {
          return (
            <div className="h-full w-full">
              <FragmentCode
                files={[
                  {
                    name: fragment.file_path,
                    content: fragment.code,
                  },
                ]}
              />
            </div>
          )
        }
        return (
          <div className="flex items-center justify-center h-full">
            <div className="text-gray-400">No code available</div>
          </div>
        )

      default:
        return (
          <div className="flex items-center justify-center h-full">
            <div className="text-gray-400">Unknown tab</div>
          </div>
        )
    }
  }

  return (
    <div className="h-full bg-black overflow-auto">
      {renderContent()}
    </div>
  )
}
