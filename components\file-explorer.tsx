'use client'

import { useState } from 'react'
import { 
  <PERSON>Text, 
  <PERSON>older, 
  <PERSON>olderOpen, 
  ChevronRight, 
  ChevronDown,
  Code,
  Database,
  Settings,
  Palette,
  TestTube,
  Globe,
  Zap,
  Package
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface FileItem {
  name: string
  content: string
  type?: string
}

interface FileNode {
  name: string
  type: 'file' | 'folder'
  children?: FileNode[]
  file?: FileItem
  path: string
}

interface FileExplorerProps {
  files: FileItem[]
  selectedFile: string | null
  onFileSelect: (fileName: string) => void
}

// Get file icon based on file extension and type
const getFileIcon = (fileName: string, fileType?: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase()
  
  // File type based icons
  if (fileType === 'api') return <Zap className="h-4 w-4 text-purple-400" />
  if (fileType === 'component') return <Code className="h-4 w-4 text-blue-400" />
  if (fileType === 'hook') return <Zap className="h-4 w-4 text-pink-400" />
  if (fileType === 'utility') return <Settings className="h-4 w-4 text-gray-400" />
  if (fileType === 'type') return <Package className="h-4 w-4 text-indigo-400" />
  if (fileType === 'database') return <Database className="h-4 w-4 text-green-400" />
  if (fileType === 'test') return <TestTube className="h-4 w-4 text-orange-400" />
  if (fileType === 'style') return <Palette className="h-4 w-4 text-pink-400" />
  
  // Extension based icons
  switch (ext) {
    case 'tsx':
    case 'jsx':
      return <Code className="h-4 w-4 text-blue-400" />
    case 'ts':
    case 'js':
      return <FileText className="h-4 w-4 text-yellow-400" />
    case 'css':
    case 'scss':
      return <Palette className="h-4 w-4 text-pink-400" />
    case 'json':
      return <Settings className="h-4 w-4 text-orange-400" />
    case 'md':
      return <FileText className="h-4 w-4 text-blue-300" />
    case 'sql':
      return <Database className="h-4 w-4 text-green-400" />
    case 'env':
      return <Settings className="h-4 w-4 text-gray-400" />
    case 'html':
      return <Globe className="h-4 w-4 text-orange-400" />
    default:
      return <FileText className="h-4 w-4 text-gray-400" />
  }
}

// Build file tree structure from flat file list
const buildFileTree = (files: FileItem[]): FileNode[] => {
  const tree: FileNode[] = []
  const folderMap = new Map<string, FileNode>()

  files.forEach(file => {
    const pathParts = file.name.split('/')
    let currentPath = ''
    
    pathParts.forEach((part, index) => {
      const isFile = index === pathParts.length - 1
      const parentPath = currentPath
      currentPath = currentPath ? `${currentPath}/${part}` : part
      
      if (isFile) {
        // Add file to tree
        const fileNode: FileNode = {
          name: part,
          type: 'file',
          file,
          path: currentPath
        }
        
        if (parentPath && folderMap.has(parentPath)) {
          const parent = folderMap.get(parentPath)!
          if (!parent.children) parent.children = []
          parent.children.push(fileNode)
        } else {
          tree.push(fileNode)
        }
      } else {
        // Add folder to tree if it doesn't exist
        if (!folderMap.has(currentPath)) {
          const folderNode: FileNode = {
            name: part,
            type: 'folder',
            children: [],
            path: currentPath
          }
          
          folderMap.set(currentPath, folderNode)
          
          if (parentPath && folderMap.has(parentPath)) {
            const parent = folderMap.get(parentPath)!
            if (!parent.children) parent.children = []
            parent.children.push(folderNode)
          } else {
            tree.push(folderNode)
          }
        }
      }
    })
  })

  return tree
}

// File tree node component
const FileTreeNode = ({ 
  node, 
  selectedFile, 
  onFileSelect, 
  level = 0 
}: { 
  node: FileNode
  selectedFile: string | null
  onFileSelect: (fileName: string) => void
  level?: number
}) => {
  const [isExpanded, setIsExpanded] = useState(true)
  
  const handleClick = () => {
    if (node.type === 'folder') {
      setIsExpanded(!isExpanded)
    } else if (node.file) {
      onFileSelect(node.file.name)
    }
  }
  
  const isSelected = node.file && selectedFile === node.file.name
  
  return (
    <div>
      <div
        className={cn(
          'flex items-center gap-1 px-2 py-1 text-sm cursor-pointer hover:bg-accent/50 rounded-sm',
          isSelected && 'bg-accent text-accent-foreground',
          'transition-colors duration-150'
        )}
        style={{ paddingLeft: `${level * 12 + 8}px` }}
        onClick={handleClick}
      >
        {node.type === 'folder' ? (
          <>
            {isExpanded ? (
              <ChevronDown className="h-3 w-3 text-muted-foreground" />
            ) : (
              <ChevronRight className="h-3 w-3 text-muted-foreground" />
            )}
            {isExpanded ? (
              <FolderOpen className="h-4 w-4 text-blue-400" />
            ) : (
              <Folder className="h-4 w-4 text-blue-400" />
            )}
          </>
        ) : (
          <>
            <div className="w-3" /> {/* Spacer for alignment */}
            {getFileIcon(node.name, node.file?.type)}
          </>
        )}
        <span className="truncate">{node.name}</span>
      </div>
      
      {node.type === 'folder' && isExpanded && node.children && (
        <div>
          {node.children.map((child, index) => (
            <FileTreeNode
              key={`child-${child.path}-${index}-${level}`}
              node={child}
              selectedFile={selectedFile}
              onFileSelect={onFileSelect}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export function FileExplorer({ files, selectedFile, onFileSelect }: FileExplorerProps) {
  const fileTree = buildFileTree(files)
  
  return (
    <div className="h-full bg-card border-r border-border">
      <div className="p-3 border-b border-border">
        <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
          Explorer
        </h3>
      </div>

      <div className="p-2 overflow-auto h-full code-editor-scrollbar">
        {fileTree.length === 0 ? (
          <div className="text-sm text-muted-foreground p-2">
            No files to display
          </div>
        ) : (
          <div className="space-y-0.5">
            {fileTree.map((node, index) => (
              <FileTreeNode
                key={`root-${node.path}-${index}`}
                node={node}
                selectedFile={selectedFile}
                onFileSelect={onFileSelect}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
