{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@11labs/react": "^0.1.4", "@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/fireworks": "^0.2.15", "@ai-sdk/google": "^1.2.22", "@ai-sdk/google-vertex": "^2.2.27", "@ai-sdk/mistral": "^1.2.8", "@ai-sdk/openai": "^1.3.23", "@e2b/code-interpreter": "^1.5.1", "@elevenlabs/client": "^0.1.7", "@elevenlabs/elevenlabs-js": "^2.2.0", "@elevenlabs/react": "^0.1.7", "@hookform/resolvers": "^3.9.1", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@radix-ui/react-visually-hidden": "^1.2.3", "@supabase/supabase-js": "^2.50.3", "@types/prismjs": "^1.26.5", "@upstash/ratelimit": "^2.0.5", "@vercel/analytics": "^1.5.0", "@vercel/kv": "^3.0.0", "ai": "^4.3.17", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "monaco-editor": "^0.52.2", "next": "15.2.4", "next-themes": "^0.4.4", "ollama-ai-provider": "^1.2.0", "posthog-js": "^1.256.2", "prismjs": "^1.30.0", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "react-textarea-autosize": "^8.5.9", "recharts": "2.15.0", "simple-icons": "^15.5.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "three": "latest", "usehooks-ts": "^3.1.1", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/three": "^0.177.0", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "eslint": "^9.30.1", "eslint-config-next": "^15.3.5", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}