@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --radius: 0.5rem;
  }
}

body {
  background-color: #0a0a0a;
  color: white;
  overflow: hidden;
}

* {
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar for chat */
.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: rgba(55, 65, 81, 0.3);
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

.font-sans {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

/* IDE Code Editor Styling */
.code-editor-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.code-editor-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

.code-editor-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
}

.code-editor-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

.code-editor-scrollbar::-webkit-scrollbar-corner {
  background: hsl(var(--muted));
}

/* Remove textarea outline completely */
textarea:focus,
textarea:focus-visible {
  outline: none !important;
  ring: none !important;
  box-shadow: none !important;
  border-color: inherit !important;
}

/* Hide Next.js development indicator */
#__next-build-watcher,
[data-nextjs-toast-wrapper],
[data-nextjs-dialog-overlay],
.__next-dev-overlay-wrapper,
.__next-dev-overlay,
[data-nextjs-scroll-focus-boundary] + div[style*="position: fixed"],
div[style*="position: fixed"][style*="bottom: 16px"][style*="left: 16px"] {
  display: none !important;
}
