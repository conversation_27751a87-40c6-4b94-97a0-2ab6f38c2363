'use client'

import { Message } from '@/lib/messages'
import { Code, FileText, Lightbulb, CheckCircle, AlertCircle } from 'lucide-react'
import { useState } from 'react'

interface EnhancedMessageProps {
  message: Message
  isLatest?: boolean
}

export function EnhancedMessage({ message, isLatest = false }: EnhancedMessageProps) {
  const [showFullCode, setShowFullCode] = useState(false)

  // Parse message content to extract different types of information
  const parseMessageContent = (text: string) => {
    const sections = {
      explanation: '',
      codeBlocks: [] as { language: string; code: string; filename?: string }[],
      fileOperations: [] as string[],
      summary: ''
    }

    const lines = text.split('\n')
    let currentSection = 'explanation'
    let currentCodeBlock = { language: '', code: '', filename: undefined as string | undefined }
    let inCodeBlock = false

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]

      if (line.startsWith('```')) {
        if (!inCodeBlock) {
          // Starting a code block
          inCodeBlock = true
          const match = line.match(/```(\w+)(?:\s+(.+))?/)
          currentCodeBlock = {
            language: match?.[1] || 'text',
            code: '',
            filename: match?.[2]
          }
        } else {
          // Ending a code block
          inCodeBlock = false
          sections.codeBlocks.push({ ...currentCodeBlock })
          currentCodeBlock = { language: '', code: '', filename: undefined }
        }
        continue
      }

      if (inCodeBlock) {
        currentCodeBlock.code += line + '\n'
      } else {
        // Look for file operations
        if (line.includes('Creating') || line.includes('Updating') || line.includes('Modified')) {
          sections.fileOperations.push(line.trim())
        } else if (line.trim()) {
          sections.explanation += line + '\n'
        }
      }
    }

    // Clean up explanation
    sections.explanation = sections.explanation.trim()
    
    // Extract summary (first few sentences)
    const sentences = sections.explanation.split(/[.!?]+/)
    sections.summary = sentences.slice(0, 2).join('. ').trim()
    if (sections.summary && !sections.summary.endsWith('.')) {
      sections.summary += '.'
    }

    return sections
  }

  if (message.role === 'user') {
    return (
      <div className="bg-gradient-to-b from-black/5 to-black/10 dark:from-black/30 dark:to-black/50 py-3 px-4 rounded-xl gap-2 w-fit max-w-[80%] ml-auto">
        {message.content.map((content, id) => {
          if (content.type === 'text') {
            return <p key={id} className="text-sm whitespace-pre-wrap">{content.text}</p>
          }
          if (content.type === 'image') {
            return (
              <img
                key={id}
                src={content.image}
                alt="uploaded"
                className="w-12 h-12 object-cover rounded-lg bg-white"
              />
            )
          }
        })}
      </div>
    )
  }

  // Agent message
  const textContent = message.content.find(c => c.type === 'text')?.text || ''
  const sections = parseMessageContent(textContent)

  return (
    <div className={`bg-accent dark:bg-white/5 border text-accent-foreground dark:text-muted-foreground rounded-2xl overflow-hidden ${isLatest ? 'ring-2 ring-blue-500/20' : ''}`}>
      {/* Header with AI indicator */}
      <div className="px-4 py-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border-b border-border/50">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
          <span className="text-xs font-medium text-blue-400">AI Assistant</span>
        </div>
      </div>

      <div className="p-4 space-y-4">
        {/* Main explanation */}
        {sections.explanation && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm">
              <Lightbulb className="w-4 h-4 text-yellow-500" />
              <span className="font-medium">Explanation</span>
            </div>
            <p className="text-sm leading-relaxed text-muted-foreground">
              {sections.explanation}
            </p>
          </div>
        )}

        {/* File operations */}
        {sections.fileOperations.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm">
              <FileText className="w-4 h-4 text-green-500" />
              <span className="font-medium">File Operations</span>
            </div>
            <div className="space-y-1">
              {sections.fileOperations.map((operation, index) => (
                <div key={index} className="flex items-center gap-2 text-xs text-muted-foreground bg-muted/30 px-2 py-1 rounded">
                  <CheckCircle className="w-3 h-3 text-green-500" />
                  <span>{operation}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Code blocks summary */}
        {sections.codeBlocks.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm">
              <Code className="w-4 h-4 text-purple-500" />
              <span className="font-medium">Code Generated</span>
            </div>
            <div className="space-y-2">
              {sections.codeBlocks.map((block, index) => (
                <div key={index} className="bg-muted/30 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <span className="text-xs font-mono bg-muted px-2 py-1 rounded">
                        {block.language}
                      </span>
                      {block.filename && (
                        <span className="text-xs text-muted-foreground">
                          {block.filename}
                        </span>
                      )}
                    </div>
                    <button
                      onClick={() => setShowFullCode(!showFullCode)}
                      className="text-xs text-blue-400 hover:text-blue-300"
                    >
                      {showFullCode ? 'Hide' : 'Show'} Code
                    </button>
                  </div>
                  
                  {showFullCode ? (
                    <pre className="text-xs bg-black/20 p-2 rounded overflow-x-auto">
                      <code>{block.code.trim()}</code>
                    </pre>
                  ) : (
                    <div className="text-xs text-muted-foreground">
                      {block.code.split('\n').length} lines • View in Code tab for full details
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Images */}
        {message.content.filter(c => c.type === 'image').map((content, id) => (
          <img
            key={id}
            src={content.image}
            alt="fragment"
            className="w-12 h-12 object-cover rounded-lg bg-white"
          />
        ))}
      </div>
    </div>
  )
}
