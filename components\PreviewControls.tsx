"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/copy-button"
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { ChevronLeft, ChevronRight, RefreshCw, Download, Maximize, Minimize, ChevronDown, RotateCw } from "lucide-react"
import TabNavigation from "@/components/TabNavigation"
import { ExecutionResult } from "@/lib/types"
import { useState } from "react"

interface PreviewControlsProps {
  result?: ExecutionResult
  onRefresh?: () => void
}

export default function PreviewControls({ result, onRefresh }: PreviewControlsProps) {
  return (
    <div className="bg-[#111111] border-b border-gray-800 px-3 py-1.5 flex items-center justify-between text-sm rounded-t-xl">
      <div className="flex items-center space-x-2">
        <TabNavigation />
      </div>
      <div className="flex items-center flex-1 justify-center space-x-2">
        <Button variant="ghost" size="icon" className="h-7 w-7 text-gray-400" disabled>
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="icon" className="h-7 w-7 text-gray-400" disabled>
          <ChevronRight className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="icon" className="h-7 w-7 text-gray-400" disabled>
          <RefreshCw className="h-4 w-4" />
        </Button>
        {result && 'url' in result ? (
          <div className="flex items-center bg-black border border-gray-700 rounded-md w-64">
            <TooltipProvider>
              <Tooltip delayDuration={0}>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7 text-gray-400"
                    onClick={onRefresh}
                  >
                    <RotateCw className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Refresh</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <span className="text-gray-400 text-xs flex-1 text-ellipsis overflow-hidden whitespace-nowrap px-2">
              {result.url}
            </span>
            <TooltipProvider>
              <Tooltip delayDuration={0}>
                <TooltipTrigger asChild>
                  <CopyButton
                    variant="ghost"
                    content={result.url}
                    className="h-7 w-7 text-gray-400"
                  />
                </TooltipTrigger>
                <TooltipContent>Copy URL</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        ) : (
          <div className="bg-black border border-gray-700 rounded-md px-3 py-1 w-64 text-center text-gray-400">/</div>
        )}
      </div>
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="icon" className="h-7 w-7 text-gray-400" disabled>
          <Download className="h-4 w-4" />
        </Button>
        <Button variant="outline" className="h-7 px-2 bg-transparent border-gray-700" disabled>
          v9
          <ChevronDown className="h-4 w-4 ml-1" />
        </Button>
        <div className="h-5 w-px bg-gray-700" />
        <Button variant="ghost" size="icon" className="h-7 w-7 text-gray-400" disabled>
          <Minimize className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="icon" className="h-7 w-7 text-gray-400" disabled>
          <Maximize className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
