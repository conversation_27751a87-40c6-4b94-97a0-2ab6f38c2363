"use client"

import { useEffect } from "react"
import { useTab } from "@/contexts/TabContext"
import { FragmentSchema } from "@/lib/schema"
import { ExecutionResult } from "@/lib/types"
import { DeepPartial } from "ai"

interface TabManagerProps {
  fragment?: DeepPartial<FragmentSchema>
  result?: ExecutionResult
}

export default function TabManager({ fragment, result }: TabManagerProps) {
  const { setPreviewEnabled, setCodeEnabled, setHasGeneratedCode, setActiveTab } = useTab()

  useEffect(() => {
    if (fragment && result) {
      // Enable tabs when code is generated
      setHasGeneratedCode(true)
      setCodeEnabled(true)
      setPreviewEnabled(true)
      setActiveTab('preview') // Switch to preview tab
    }
  }, [fragment, result, setHasGeneratedCode, setCodeEnabled, setPreviewEnabled, setActiveTab])

  return null // This component doesn't render anything
}
