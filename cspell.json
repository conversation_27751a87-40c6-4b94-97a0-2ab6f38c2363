{"version": "0.2", "language": "en", "words": ["shadcn", "numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seaborn", "plotly", "streamlit", "Streamlit", "gradio", "Gradio", "<PERSON><PERSON><PERSON>", "nuxt", "tailwindcss", "Tailwind", "<PERSON><PERSON><PERSON>", "lucide", "Lucide", "framer", "Framer", "supabase", "Supabase", "vercel", "Vercel", "nextjs", "Next.js", "typescript", "TypeScript", "javascript", "JavaScript", "prisma", "Prisma", "posthog", "PostHog", "elevenlabs", "ElevenLabs", "openai", "OpenAI", "anthropic", "Anthropic", "mistral", "<PERSON><PERSON><PERSON>", "fireworks", "Fireworks", "groq", "Groq", "upstash", "Up<PERSON>sh", "ratelimit", "ratelimiting", "middleware", "middlewares", "auth", "o<PERSON>h", "OAuth", "jwt", "JWT", "api", "API", "crud", "CRUD", "ui", "UI", "ux", "UX", "css", "CSS", "html", "HTML", "jsx", "JSX", "tsx", "TSX", "json", "JSON", "yaml", "YAML", "toml", "TOML", "env", "ENV", "config", "configs", "eslint", "ESLint", "prettier", "<PERSON>ttier", "webpack", "Webpack", "vite", "Vite", "turbo", "Turbo", "turbopack", "Turbopack", "monorepo", "monorepos", "repo", "repos", "github", "GitHub", "gitlab", "GitLab", "bitbucket", "Bitbucket", "npm", "NPM", "yarn", "Yarn", "pnpm", "PNPM", "bun", "<PERSON>un", "deno", "<PERSON><PERSON>", "node", "Node", "nodejs", "Node.js", "react", "React", "vue", "<PERSON><PERSON>", "angular", "Angular", "svelte", "Svelte", "solid", "Solid", "astro", "Astro", "remix", "Remix", "gatsby", "Gatsby", "express", "Express", "fastify", "Fastify", "koa", "<PERSON><PERSON>", "hapi", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "NestJS", "trpc", "tRPC", "graphql", "GraphQL", "apollo", "Apollo", "relay", "<PERSON><PERSON>", "mongodb", "MongoDB", "postgres", "PostgreSQL", "mysql", "MySQL", "sqlite", "SQLite", "redis", "Redis", "elasticsearch", "Elasticsearch", "solr", "Solr", "clickhouse", "ClickHouse", "snowflake", "Snowflake", "big<PERSON>y", "<PERSON><PERSON><PERSON><PERSON>", "dynamodb", "DynamoDB", "firestore", "Firestore", "planetscale", "PlanetScale", "neon", "Neon", "railway", "Railway", "render", "Render", "netlify", "Netlify", "cloudflare", "Cloudflare", "aws", "AWS", "azure", "Azure", "gcp", "GCP", "docker", "<PERSON>er", "kubernetes", "Kubernetes", "helm", "<PERSON><PERSON>", "terraform", "Terraform", "ansible", "Ansible", "jenkins", "<PERSON>", "github", "GitHub", "actions", "Actions", "workflows", "workflow", "cicd", "CI/CD", "devops", "DevOps", "sre", "SRE", "observability", "telemetry", "monitoring", "logging", "tracing", "metrics", "alerting", "dashboards", "dashboard", "analytics", "visualisation", "visualization", "charts", "chart", "graphs", "graph", "pandas", "jup<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ipynb", "sklearn", "scikit", "tensorflow", "TensorFlow", "pytorch", "PyTorch", "keras", "<PERSON><PERSON>", "opencv", "OpenCV", "pillow", "PIL", "requests", "flask", "Flask", "django", "Django", "<PERSON><PERSON><PERSON>", "FastAPI", "pydantic", "Pydantic", "sqlalchemy", "SQLAlchemy", "alembic", "Alembic", "celery", "Celery", "redis", "Redis", "rabbitmq", "RabbitMQ", "kafka", "Kafka", "elasticsearch", "Elasticsearch"], "ignorePaths": ["node_modules/**", ".next/**", "dist/**", "build/**", "coverage/**", ".git/**", "*.min.js", "*.min.css", "package-lock.json", "yarn.lock", "pnpm-lock.yaml"], "ignoreRegExpList": ["/\\b[A-Z]{2,}\\b/g", "/\\b\\d+\\b/g", "/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}/gi", "/\\b[a-f0-9]{40}\\b/gi", "/\\b[a-f0-9]{64}\\b/gi"], "allowCompoundWords": true, "caseSensitive": false}