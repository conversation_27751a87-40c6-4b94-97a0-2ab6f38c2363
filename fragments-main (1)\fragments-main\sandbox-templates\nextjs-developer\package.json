{"name": "nextjs-elite-sandbox", "version": "1.0.0", "private": true, "description": "Elite Next.js sandbox environment optimized for AI development", "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "analyze": "ANALYZE=true next build", "clean": "rm -rf .next out dist node_modules/.cache"}, "dependencies": {"next": "^15.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "typescript": "^5.6.0", "sharp": "^0.33.0", "@next/bundle-analyzer": "^15.0.0", "@vercel/analytics": "^1.3.0", "react-hook-form": "^7.53.0", "zod": "^3.23.0", "clsx": "^2.1.0", "class-variance-authority": "^0.7.0", "lucide-react": "^0.445.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.0", "@radix-ui/react-dialog": "^1.1.0", "@radix-ui/react-dropdown-menu": "^2.1.0", "@radix-ui/react-select": "^2.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.0", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.0", "@radix-ui/react-hover-card": "^1.1.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-sheet": "^1.1.0", "@radix-ui/react-skeleton": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-table": "^1.1.0", "@radix-ui/react-textarea": "^1.1.0", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.0", "axios": "^1.7.0", "swr": "^2.2.0", "@tanstack/react-query": "^5.56.0", "@tanstack/react-query-devtools": "^5.56.0", "framer-motion": "^11.5.0", "react-hot-toast": "^2.4.0", "next-themes": "^0.3.0", "date-fns": "^4.1.0", "lodash": "^4.17.21", "uuid": "^10.0.0"}, "devDependencies": {"@types/node": "^22.7.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@types/lodash": "^4.17.0", "@types/uuid": "^10.0.0", "eslint": "^8.57.0", "eslint-config-next": "^15.0.0", "@typescript-eslint/eslint-plugin": "^8.7.0", "@typescript-eslint/parser": "^8.7.0", "prettier": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "vitest": "^2.1.0", "@vitejs/plugin-react": "^4.3.0", "jsdom": "^25.0.0", "@testing-library/react": "^16.0.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/user-event": "^14.5.0", "@vitest/coverage-v8": "^2.1.0", "@vitest/ui": "^2.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}