import { z } from 'zod'

// File schema for multi-file applications
export const fileSchema = z.object({
  file_name: z.string().describe('Name of the file (e.g., "UserCard.tsx", "api.ts", "schema.sql").'),
  file_path: z.string().describe('Relative path to the file, including the file name (e.g., "components/UserCard.tsx", "lib/api.ts", "database/schema.sql").'),
  file_content: z.string().describe('Complete content of the file. Must be valid, runnable code.'),
  file_type: z.enum(['component', 'page', 'api', 'utility', 'config', 'style', 'database', 'middleware', 'hook', 'type', 'test']).describe('Type of file for better organization.'),
  description: z.string().describe('Brief description of what this file does (1 sentence).'),
})

export const fragmentSchema = z.object({
  commentary: z.string().describe(`Describe what you're about to do and the steps you want to take for generating the full-stack application in great detail. Include architecture decisions, file structure, and implementation approach.`),
  template: z.string().describe('Name of the template used to generate the fragment.'),
  title: z.string().describe('Short title of the application. Max 4 words.'),
  description: z.string().describe('Short description of the full-stack application. Max 2 sentences.'),
  additional_dependencies: z.array(z.string()).describe('Additional dependencies required by the application. Do not include dependencies that are already included in the template.'),
  has_additional_dependencies: z.boolean().describe('Detect if additional dependencies that are not included in the template are required by the application.'),
  install_dependencies_command: z.string().describe('Command to install additional dependencies required by the application.'),
  port: z.number().nullable().describe('Port number used by the application. Null when no ports are exposed.'),

  // Multi-file support
  is_multi_file: z.boolean().describe('True if this is a multi-file application with multiple components, pages, API routes, etc. False for simple single-file demos.'),

  // Single file (for simple demos/prototypes)
  file_path: z.string().optional().describe('Relative path to the main file when is_multi_file is false.'),
  code: z.string().optional().describe('Code for the main file when is_multi_file is false.'),

  // Multi-file structure (for full applications)
  files: z.array(fileSchema).optional().describe('Array of all files that make up the full-stack application when is_multi_file is true.'),

  // Application architecture
  architecture: z.object({
    frontend: z.string().describe('Frontend architecture description (e.g., "React components with TypeScript, Tailwind CSS for styling").'),
    backend: z.string().describe('Backend architecture description (e.g., "Next.js API routes with database integration").'),
    database: z.string().optional().describe('Database setup if applicable (e.g., "SQLite with Prisma ORM", "JSON file storage").'),
    authentication: z.string().optional().describe('Authentication approach if applicable (e.g., "NextAuth.js with GitHub provider").'),
    deployment: z.string().describe('Deployment considerations (e.g., "Vercel-ready with environment variables").'),
  }).optional().describe('Architecture overview for multi-file applications.'),

  // Entry points
  main_file: z.string().optional().describe('Path to the main entry file (e.g., "pages/index.tsx" or "app/page.tsx").'),
  api_routes: z.array(z.string()).optional().describe('List of API route paths (e.g., ["/api/users", "/api/auth"]).'),
})

export type FragmentSchema = z.infer<typeof fragmentSchema>
