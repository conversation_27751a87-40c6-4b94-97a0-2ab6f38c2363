{"code-interpreter-v1": {"name": "Python data analyst", "lib": ["python", "jup<PERSON><PERSON>", "numpy", "pandas", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seaborn", "plotly"], "file": "script.py", "instructions": "Runs code as a Jupyter notebook cell. Strong data analysis angle. Can use complex visualisation to explain results.", "port": null}, "nextjs-developer": {"name": "Next.js developer", "lib": ["nextjs@14.2.5", "typescript", "@types/node", "@types/react", "@types/react-dom", "postcss", "tailwindcss", "shadcn"], "file": "pages/index.tsx", "instructions": "A Next.js 13+ app that reloads automatically. Using the pages router.", "port": 3000}, "vue-developer": {"name": "Vue.js developer", "lib": ["vue@latest", "nuxt@3.13.0", "tailwindcss"], "file": "app.vue", "instructions": "A Vue.js 3+ app that reloads automatically. Only when asked specifically for a Vue app.", "port": 3000}, "streamlit-developer": {"name": "Streamlit developer", "lib": ["streamlit", "pandas", "numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "request", "seaborn", "plotly"], "file": "app.py", "instructions": "A streamlit app that reloads automatically.", "port": 8501}, "gradio-developer": {"name": "Gradio developer", "lib": ["gradio", "pandas", "numpy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "request", "seaborn", "plotly"], "file": "app.py", "instructions": "A gradio app. Gradio Blocks/Interface should be called demo.", "port": 7860}}