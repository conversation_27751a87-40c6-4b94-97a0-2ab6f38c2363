"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Plus, X } from "lucide-react"

export default function ChatTabManager() {
  // Mock tab data for UI display
  const mockTabs = [
    { id: '1', title: 'New Chat', isActive: true },
    { id: '2', title: 'React Component', isActive: false },
  ]

  return (
    <div className="bg-[#0a0a0a] border-b border-gray-800 px-4 py-2">
      <div className="flex items-center space-x-1">
        {mockTabs.map((tab) => (
          <div
            key={tab.id}
            className={`group flex items-center space-x-2 px-3 py-1.5 rounded-md text-sm transition-colors ${
              tab.isActive
                ? 'bg-gray-800 text-white'
                : 'text-gray-400 hover:bg-gray-900 hover:text-gray-300'
            }`}
          >
            <span className="truncate max-w-[120px]">{tab.title}</span>
            <Button
              variant="ghost"
              size="icon"
              className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity text-gray-400 hover:text-gray-300"
              disabled
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        ))}
        
        <Button
          variant="ghost"
          size="icon"
          className="h-7 w-7 text-gray-400 hover:text-gray-300 hover:bg-gray-900"
          disabled
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
