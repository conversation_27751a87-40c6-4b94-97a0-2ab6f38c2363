@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;
    
    /* Chart colors */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    /* Dark theme colors */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    
    /* Dark chart colors */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Improved scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-md;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
  
  /* Firefox scrollbar */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.3) hsl(var(--muted));
  }
  
  /* Focus styles */
  :focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }
  
  /* Selection styles */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }
  
  /* Improved typography */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }
  
  h1 {
    @apply text-4xl lg:text-5xl;
  }
  
  h2 {
    @apply text-3xl lg:text-4xl;
  }
  
  h3 {
    @apply text-2xl lg:text-3xl;
  }
  
  h4 {
    @apply text-xl lg:text-2xl;
  }
  
  h5 {
    @apply text-lg lg:text-xl;
  }
  
  h6 {
    @apply text-base lg:text-lg;
  }
  
  p {
    @apply leading-7;
  }
  
  /* Link styles */
  a {
    @apply text-primary underline-offset-4 hover:underline;
  }
  
  /* Code styles */
  code {
    @apply relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold;
  }
  
  pre {
    @apply overflow-x-auto rounded-lg bg-muted p-4;
  }
  
  pre code {
    @apply bg-transparent p-0;
  }
  
  /* Table styles */
  table {
    @apply w-full border-collapse border-spacing-0;
  }
  
  th {
    @apply border-b border-border bg-muted/50 px-4 py-2 text-left font-medium;
  }
  
  td {
    @apply border-b border-border px-4 py-2;
  }
  
  /* Form styles */
  input, textarea, select {
    @apply transition-colors;
  }
  
  input:disabled, textarea:disabled, select:disabled {
    @apply cursor-not-allowed opacity-50;
  }
  
  /* Button focus styles */
  button:focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2;
  }
  
  /* Improved animations */
  @media (prefers-reduced-motion: no-preference) {
    * {
      @apply transition-colors duration-200;
    }
  }
  
  /* Print styles */
  @media print {
    * {
      @apply text-black bg-white;
    }
    
    .no-print {
      @apply hidden;
    }
  }
}

@layer components {
  /* Custom component styles */
  .container {
    @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  }
  
  .prose {
    @apply max-w-none text-foreground;
  }
  
  .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    @apply text-foreground;
  }
  
  .prose a {
    @apply text-primary no-underline hover:underline;
  }
  
  .prose code {
    @apply text-foreground;
  }
  
  .prose pre {
    @apply bg-muted;
  }
  
  .prose blockquote {
    @apply border-l-primary text-foreground;
  }
}

@layer utilities {
  /* Custom utility classes */
  .text-balance {
    text-wrap: balance;
  }
  
  .text-pretty {
    text-wrap: pretty;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .gradient-text {
    @apply bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent;
  }
  
  .glass {
    @apply bg-background/80 backdrop-blur-sm border border-border/50;
  }
  
  .animate-in {
    animation-duration: 0.2s;
    animation-fill-mode: both;
  }
  
  .animate-out {
    animation-duration: 0.15s;
    animation-fill-mode: both;
  }
}
