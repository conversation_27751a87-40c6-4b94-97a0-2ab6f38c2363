# Use latest Node.js LTS with full features for optimal development experience
FROM node:22-bullseye

# Install essential development tools and dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    vim \
    nano \
    htop \
    build-essential \
    python3 \
    python3-pip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Set up optimal Node.js environment
ENV NODE_ENV=development
ENV NEXT_TELEMETRY_DISABLED=1
ENV TURBOPACK=1

# Copy startup script
COPY compile_page.sh /compile_page.sh
RUN chmod +x /compile_page.sh

# Create optimized Next.js application
WORKDIR /home/<USER>/nextjs-app

# Create Next.js app with latest version and optimal settings
RUN npx create-next-app@latest . \
    --typescript \
    --tailwind \
    --eslint \
    --app \
    --src-dir \
    --import-alias "@/*" \
    --use-npm \
    --turbo

# Copy optimized configuration files
COPY next.config.ts ./
COPY tsconfig.json ./
COPY tailwind.config.ts ./
COPY .eslintrc.json ./
COPY _app.tsx src/pages/_app.tsx

# Copy styles directory for CSS module compatibility
COPY styles ./styles
COPY src/styles ./src/styles

# Install comprehensive development dependencies
RUN npm install -D \
    @types/node@latest \
    @types/react@latest \
    @types/react-dom@latest \
    eslint-config-next@latest \
    prettier \
    @typescript-eslint/eslint-plugin \
    @typescript-eslint/parser \
    autoprefixer \
    postcss

# Install production dependencies for enhanced functionality
RUN npm install \
    sharp \
    @next/bundle-analyzer \
    @vercel/analytics \
    react-hook-form \
    zod \
    clsx \
    class-variance-authority \
    lucide-react \
    @radix-ui/react-slot \
    @radix-ui/react-toast \
    @radix-ui/react-dialog \
    @radix-ui/react-dropdown-menu \
    @radix-ui/react-select \
    @radix-ui/react-tabs \
    @radix-ui/react-accordion \
    @radix-ui/react-alert-dialog \
    @radix-ui/react-avatar \
    @radix-ui/react-button \
    @radix-ui/react-card \
    @radix-ui/react-checkbox \
    @radix-ui/react-collapsible \
    @radix-ui/react-context-menu \
    @radix-ui/react-hover-card \
    @radix-ui/react-input \
    @radix-ui/react-label \
    @radix-ui/react-menubar \
    @radix-ui/react-navigation-menu \
    @radix-ui/react-popover \
    @radix-ui/react-progress \
    @radix-ui/react-radio-group \
    @radix-ui/react-scroll-area \
    @radix-ui/react-separator \
    @radix-ui/react-sheet \
    @radix-ui/react-skeleton \
    @radix-ui/react-slider \
    @radix-ui/react-switch \
    @radix-ui/react-table \
    @radix-ui/react-textarea \
    @radix-ui/react-toggle \
    @radix-ui/react-toggle-group \
    @radix-ui/react-tooltip

# Initialize shadcn/ui with all components
RUN npx shadcn@latest init -d --yes
RUN npx shadcn@latest add --all --yes

# Install additional useful packages for full-stack development
RUN npm install \
    axios \
    swr \
    react-query \
    @tanstack/react-query \
    framer-motion \
    react-hot-toast \
    date-fns \
    lodash \
    @types/lodash \
    uuid \
    @types/uuid

# Set up testing environment
RUN npm install -D \
    vitest \
    @vitejs/plugin-react \
    jsdom \
    @testing-library/react \
    @testing-library/dom \
    @testing-library/jest-dom \
    @testing-library/user-event

# Move the Next.js app to the home directory and remove the nextjs-app directory
RUN mv /home/<USER>/nextjs-app/* /home/<USER>/ && rm -rf /home/<USER>/nextjs-app

# Set proper permissions
RUN chown -R user:user /home/<USER>
