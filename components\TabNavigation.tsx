"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Globe, Eye, Code, Lock } from "lucide-react"
import { useTab, TabType } from "@/contexts/TabContext"

export default function TabNavigation() {
  const { 
    activeTab, 
    setActiveTab, 
    isPreviewEnabled, 
    isCodeEnabled,
    hasGeneratedCode 
  } = useTab()

  const tabs = [
    {
      id: 'orb' as TabType,
      label: 'ORB',
      icon: Globe,
      enabled: true,
      description: 'The Orb'
    },
    {
      id: 'preview' as TabType,
      label: 'Preview',
      icon: Eye,
      enabled: isPreviewEnabled,
      description: 'Live Preview'
    },
    {
      id: 'code' as TabType,
      label: 'Code',
      icon: Code,
      enabled: isCodeEnabled,
      description: 'File Explorer'
    }
  ]

  return (
    <div className="flex items-center space-x-1 bg-gray-900/50 rounded-lg p-1">
      {tabs.map((tab) => {
        const Icon = tab.icon
        const isActive = activeTab === tab.id
        const isDisabled = !tab.enabled

        return (
          <Button
            key={tab.id}
            variant={isActive ? "default" : "ghost"}
            size="sm"
            disabled={isDisabled}
            onClick={() => tab.enabled && setActiveTab(tab.id)}
            className={`
              relative h-8 px-3 text-xs font-medium transition-all duration-200
              ${isActive 
                ? 'bg-white text-black shadow-sm' 
                : isDisabled 
                  ? 'text-gray-500 cursor-not-allowed opacity-50' 
                  : 'text-gray-300 hover:text-white hover:bg-gray-800'
              }
            `}
          >
            <Icon className="h-3 w-3 mr-1.5" />
            {tab.label}
            
            {/* Disabled indicator */}
            {isDisabled && (
              <Lock className="h-2 w-2 ml-1 opacity-60" />
            )}
            
            {/* New content indicator */}
            {tab.id === 'preview' && hasGeneratedCode && !isActive && (
              <Badge 
                variant="secondary" 
                className="absolute -top-1 -right-1 h-2 w-2 p-0 bg-green-500 border-0"
              />
            )}
          </Button>
        )
      })}
      
      {/* Tab description tooltip */}
      <div className="hidden md:flex items-center ml-4 text-xs text-gray-400">
        {tabs.find(tab => tab.id === activeTab)?.description}
      </div>
    </div>
  )
}
