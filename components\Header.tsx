"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Settings, Share, Github, ChevronDown, Lock } from "lucide-react"
import Image from "next/image"

export default function Header() {
  return (
    <header className="bg-[#111111] border-b border-gray-800 px-4 py-2 flex items-center justify-between z-50">
      {/* Left Section */}
      <div className="flex items-center space-x-3 text-sm">
        <Image
          src="/logo.png"
          alt="Logo"
          width={64}
          height={64}
          className="object-contain"
        />
        <span className="text-gray-500">/</span>
        <div className="flex items-center space-x-2">
          <Avatar className="h-6 w-6">
            <AvatarImage src="https://avatar.vercel.sh/personal" />
            <AvatarFallback className="bg-gray-700 text-xs">P</AvatarFallback>
          </Avatar>
          <span>Personal</span>
        </div>
        <Button variant="outline" size="sm" className="h-6 px-2 text-xs bg-transparent border-gray-700">
          Free
        </Button>
        <span className="text-gray-500">/</span>
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-sm" />
          <span>The Orb</span>
        </div>
        <span className="text-gray-500">/</span>
        <span>The Orb</span>
        <Button variant="outline" size="sm" className="h-6 px-2 text-xs bg-transparent border-gray-700">
          <Lock className="h-3 w-3 mr-1" />
          Private
        </Button>
      </div>

      {/* Right Section */}
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="icon" className="h-8 w-8 text-gray-400 hover:text-white">
          <Settings className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="icon" className="h-8 w-8 text-gray-400 hover:text-white">
          <Share className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="icon" className="h-8 w-8 text-gray-400 hover:text-white">
          <Github className="h-4 w-4" />
        </Button>
        <Button className="h-8 px-3 text-sm bg-white text-black hover:bg-gray-200">
          Publish
          <ChevronDown className="h-4 w-4 ml-1" />
        </Button>
        <Avatar className="h-8 w-8 cursor-pointer">
          <div className="w-full h-full rounded-full bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500 p-0.5">
            <div className="w-full h-full bg-gray-800 rounded-full" />
          </div>
        </Avatar>
      </div>
    </header>
  )
}
