import { ExecutionResultWeb } from '@/lib/types'

export function FragmentWeb({ result, refreshKey }: { result: ExecutionResultWeb, refreshKey?: number }) {
  if (!result) return null

  return (
    <div className="flex flex-col w-full h-full">
      <iframe
        key={refreshKey || 0}
        className="h-full w-full"
        sandbox="allow-forms allow-scripts allow-same-origin"
        loading="lazy"
        src={result.url}
      />
    </div>
  )
}
