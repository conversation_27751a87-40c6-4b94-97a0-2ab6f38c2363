# This is a config for E2B sandbox template.
# You can use template ID (rtnj5ynpykzr3jz95pmw) or template name (streamlit-developer) to create a sandbox:

# Python SDK
# from e2b import Sandbox, AsyncSandbox
# sandbox = Sandbox("streamlit-developer") # Sync sandbox
# sandbox = await AsyncSandbox.create("streamlit-developer") # Async sandbox

# JS SDK
# import { Sandbox } from 'e2b'
# const sandbox = await Sandbox.create('streamlit-developer')

template_id = "rtnj5ynpykzr3jz95pmw"
dockerfile = "e2b.Dockerfile"
template_name = "streamlit-developer"
start_cmd = "cd /home/<USER>"
cpu_count = 4
memory_mb = 4_096
team_id = "460355b3-4f64-48f9-9a16-4442817f79f5"
