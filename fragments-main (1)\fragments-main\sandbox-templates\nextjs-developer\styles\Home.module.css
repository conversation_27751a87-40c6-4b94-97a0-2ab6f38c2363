/* Basic CSS Module for compatibility */
/* This file provides fallback styles when AI generates CSS module imports */
/* Prefer using Tailwind CSS classes directly instead */

.container {
  @apply min-h-screen py-0 px-2 flex flex-col justify-center items-center;
}

.main {
  @apply py-20 px-0 flex-1 flex flex-col justify-center items-center;
}

.title {
  @apply text-6xl font-bold text-center leading-tight;
}

.title a {
  @apply text-blue-600 no-underline hover:underline focus:underline active:underline;
}

.description {
  @apply text-xl text-center leading-relaxed;
}

.code {
  @apply bg-gray-100 rounded-md p-3 font-mono text-lg;
}

.grid {
  @apply flex items-center justify-center flex-wrap max-w-4xl;
}

.card {
  @apply m-4 p-6 text-left no-underline border border-gray-300 rounded-lg transition-colors duration-150 ease-in-out;
}

.card:hover,
.card:focus,
.card:active {
  @apply text-blue-600 border-blue-600;
}

.card h2 {
  @apply text-2xl font-semibold mb-4;
}

.card p {
  @apply text-lg leading-relaxed;
}

/* Responsive design */
@media (max-width: 600px) {
  .grid {
    @apply w-full flex-col;
  }
  
  .title {
    @apply text-4xl;
  }
  
  .description {
    @apply text-lg;
  }
}
