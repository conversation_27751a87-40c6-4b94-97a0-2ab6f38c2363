import { Message } from '@/lib/messages'
import { FragmentSchema } from '@/lib/schema'
import { ExecutionResult } from '@/lib/types'
import { DeepPartial } from 'ai'
import { LoaderIcon, Terminal } from 'lucide-react'
import { useEffect } from 'react'
import { StreamingStatus, StreamingUpdate } from './streaming-status'
import { EnhancedMessage } from './enhanced-message'





export function Chat({
  messages,
  isLoading,
  setCurrentPreview,
  streamingUpdates = [],
}: {
  messages: Message[]
  isLoading: boolean
  setCurrentPreview: (preview: {
    fragment: DeepPartial<FragmentSchema> | undefined
    result: ExecutionResult | undefined
  }) => void
  streamingUpdates?: StreamingUpdate[]
}) {
  useEffect(() => {
    const chatContainer = document.getElementById('chat-container')
    if (chatContainer) {
      chatContainer.scrollTop = chatContainer.scrollHeight
    }
  }, [JSON.stringify(messages), isLoading])

  return (
    <div
      id="chat-container"
      className="flex flex-col pb-12 gap-3 overflow-y-auto max-h-full px-4"
    >
      {messages.map((message: Message, index: number) => (
        <div key={index} className="flex flex-col gap-3">
          <EnhancedMessage
            message={message}
            isLatest={index === messages.length - 1}
          />

          {message.object && (
            <div
              onClick={() =>
                setCurrentPreview({
                  fragment: message.object,
                  result: message.result,
                })
              }
              className="py-3 px-4 w-full flex items-center border rounded-xl select-none hover:bg-white dark:hover:bg-white/5 hover:cursor-pointer transition-colors"
            >
              <div className="rounded-lg w-10 h-10 bg-orange-500/10 flex items-center justify-center">
                <Terminal strokeWidth={2} className="text-orange-500 w-5 h-5" />
              </div>
              <div className="pl-3 flex flex-col">
                <span className="font-semibold text-sm text-primary">
                  {message.object.title}
                </span>
                <span className="text-xs text-muted-foreground">
                  Click to view in preview
                </span>
              </div>
            </div>
          )}
        </div>
      ))}

      {isLoading && (
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <LoaderIcon strokeWidth={2} className="animate-spin w-4 h-4" />
            <span>AI Assistant is working...</span>
          </div>
          <StreamingStatus isActive={isLoading} updates={streamingUpdates} />
        </div>
      )}
    </div>
  )
}
