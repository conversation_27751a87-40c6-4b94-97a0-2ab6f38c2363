'use client'

import { useState, useEffect } from 'react'
import { FileText, Code, Zap, CheckCircle, Clock } from 'lucide-react'

export interface StreamingUpdate {
  type: 'file' | 'analysis' | 'generation' | 'completion'
  message: string
  fileName?: string
  progress?: number
  timestamp: number
}

interface StreamingStatusProps {
  isActive: boolean
  updates?: StreamingUpdate[]
}

export function StreamingStatus({ isActive, updates = [] }: StreamingStatusProps) {
  const [currentUpdate, setCurrentUpdate] = useState<StreamingUpdate | null>(null)
  const [allUpdates, setAllUpdates] = useState<StreamingUpdate[]>([])

  useEffect(() => {
    if (updates.length > 0) {
      setAllUpdates(updates)
      setCurrentUpdate(updates[updates.length - 1])
    }
  }, [updates])

  // Simulate realistic streaming updates when no real updates are provided
  useEffect(() => {
    if (isActive && updates.length === 0) {
      const simulatedUpdates: StreamingUpdate[] = [
        { type: 'analysis', message: 'Analyzing requirements...', timestamp: Date.now() },
        { type: 'generation', message: 'Planning component structure...', timestamp: Date.now() + 1000 },
        { type: 'file', message: 'Creating main component...', fileName: 'App.tsx', timestamp: Date.now() + 2000 },
        { type: 'file', message: 'Setting up styles...', fileName: 'globals.css', timestamp: Date.now() + 3000 },
        { type: 'file', message: 'Configuring dependencies...', fileName: 'package.json', timestamp: Date.now() + 4000 },
        { type: 'generation', message: 'Optimizing code structure...', timestamp: Date.now() + 5000 },
        { type: 'completion', message: 'Application ready!', timestamp: Date.now() + 6000 }
      ]

      let currentIndex = 0
      const interval = setInterval(() => {
        if (currentIndex < simulatedUpdates.length) {
          const update = simulatedUpdates[currentIndex]
          setCurrentUpdate(update)
          setAllUpdates(prev => [...prev, update])
          currentIndex++
        } else {
          clearInterval(interval)
        }
      }, 1500)

      return () => clearInterval(interval)
    } else if (!isActive) {
      setCurrentUpdate(null)
      setAllUpdates([])
    }
  }, [isActive, updates.length])

  if (!isActive && allUpdates.length === 0) return null

  const getIcon = (type: StreamingUpdate['type']) => {
    switch (type) {
      case 'file':
        return <FileText className="w-4 h-4" />
      case 'analysis':
        return <Zap className="w-4 h-4" />
      case 'generation':
        return <Code className="w-4 h-4" />
      case 'completion':
        return <CheckCircle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  const getColor = (type: StreamingUpdate['type']) => {
    switch (type) {
      case 'file':
        return 'text-blue-400 bg-blue-400/10'
      case 'analysis':
        return 'text-purple-400 bg-purple-400/10'
      case 'generation':
        return 'text-green-400 bg-green-400/10'
      case 'completion':
        return 'text-emerald-400 bg-emerald-400/10'
      default:
        return 'text-gray-400 bg-gray-400/10'
    }
  }

  return (
    <div className="space-y-2">
      {/* Current active status */}
      {currentUpdate && isActive && (
        <div className={`flex items-center gap-2 text-sm px-3 py-2 rounded-lg ${getColor(currentUpdate.type)}`}>
          <div className="animate-pulse">
            {getIcon(currentUpdate.type)}
          </div>
          <span>{currentUpdate.message}</span>
          {currentUpdate.fileName && (
            <span className="text-xs opacity-75">({currentUpdate.fileName})</span>
          )}
        </div>
      )}

      {/* Recent updates history (show last 3) */}
      {allUpdates.length > 1 && (
        <div className="space-y-1">
          {allUpdates.slice(-3, -1).map((update, index) => (
            <div
              key={update.timestamp}
              className={`flex items-center gap-2 text-xs px-2 py-1 rounded opacity-60 ${getColor(update.type)}`}
            >
              {getIcon(update.type)}
              <span>{update.message}</span>
              {update.fileName && (
                <span className="opacity-75">({update.fileName})</span>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

// Hook for managing streaming updates
export function useStreamingUpdates() {
  const [updates, setUpdates] = useState<StreamingUpdate[]>([])
  const [isStreaming, setIsStreaming] = useState(false)

  const addUpdate = (update: Omit<StreamingUpdate, 'timestamp'>) => {
    const newUpdate: StreamingUpdate = {
      ...update,
      timestamp: Date.now()
    }
    setUpdates(prev => [...prev, newUpdate])
  }

  const startStreaming = () => {
    setIsStreaming(true)
    setUpdates([])
  }

  const stopStreaming = () => {
    setIsStreaming(false)
  }

  const clearUpdates = () => {
    setUpdates([])
  }

  return {
    updates,
    isStreaming,
    addUpdate,
    startStreaming,
    stopStreaming,
    clearUpdates
  }
}
