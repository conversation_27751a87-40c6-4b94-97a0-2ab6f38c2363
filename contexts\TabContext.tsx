"use client"

import { createContext, useContext, useState, ReactNode } from 'react'

export type TabType = 'orb' | 'preview' | 'code'

interface TabContextType {
  activeTab: TabType
  setActiveTab: (tab: TabType) => void
  isPreviewEnabled: boolean
  setPreviewEnabled: (enabled: boolean) => void
  isCodeEnabled: boolean
  setCodeEnabled: (enabled: boolean) => void
  hasGeneratedCode: boolean
  setHasGeneratedCode: (generated: boolean) => void
}

const TabContext = createContext<TabContextType | undefined>(undefined)

export function TabProvider({ children }: { children: ReactNode }) {
  const [activeTab, setActiveTab] = useState<TabType>('orb')
  const [isPreviewEnabled, setPreviewEnabled] = useState(false)
  const [isCodeEnabled, setCodeEnabled] = useState(false)
  const [hasGeneratedCode, setHasGeneratedCode] = useState(false)

  return (
    <TabContext.Provider
      value={{
        activeTab,
        setActiveTab,
        isPreviewEnabled,
        setPreviewEnabled,
        isCodeEnabled,
        setCodeEnabled,
        hasGeneratedCode,
        setHasGeneratedCode,
      }}
    >
      {children}
    </TabContext.Provider>
  )
}

export function useTab() {
  const context = useContext(TabContext)
  if (context === undefined) {
    throw new Error('useTab must be used within a TabProvider')
  }
  return context
}
