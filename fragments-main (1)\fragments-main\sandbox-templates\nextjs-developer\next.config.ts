import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
  // Enable experimental features for optimal performance
  experimental: {
    // Enable React Compiler for automatic optimizations
    reactCompiler: true,
    // Enable Partial Prerendering for better performance
    ppr: 'incremental',
    // Enable Turbopack for faster builds
    turbo: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
    },
    // Optimize package imports for better tree shaking
    optimizePackageImports: [
      'lucide-react',
      '@radix-ui/react-icons',
      'date-fns',
      'lodash',
      'framer-motion',
    ],
  },

  // Enable TypeScript strict mode
  typescript: {
    // Enable strict type checking
    ignoreBuildErrors: false,
  },

  // ESLint configuration
  eslint: {
    // Don't ignore during builds to maintain code quality
    ignoreDuringBuilds: false,
  },

  // Enable React Strict Mode for better development experience
  reactStrictMode: true,

  // Optimize images with Sharp
  images: {
    formats: ['image/avif', 'image/webp'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60,
  },

  // Enable source maps in production for better debugging
  productionBrowserSourceMaps: true,

  // Optimize bundle analysis
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Optimize bundle size
    if (!dev && !isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          default: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true,
          },
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            priority: -10,
            chunks: 'all',
          },
          react: {
            test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
            name: 'react',
            priority: 20,
            chunks: 'all',
          },
          radix: {
            test: /[\\/]node_modules[\\/]@radix-ui[\\/]/,
            name: 'radix',
            priority: 15,
            chunks: 'all',
          },
        },
      }
    }

    // Add support for importing SVGs as React components
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    })

    return config
  },

  // Headers for security and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
    ]
  },

  // Enable compression
  compress: true,

  // Optimize for development
  onDemandEntries: {
    // Period (in ms) where the server will keep pages in the buffer
    maxInactiveAge: 25 * 1000,
    // Number of pages that should be kept simultaneously without being disposed
    pagesBufferLength: 2,
  },

  // Enable logging for better debugging
  logging: {
    fetches: {
      fullUrl: true,
    },
  },

  // Environment variables
  env: {
    NEXT_PUBLIC_APP_ENV: 'development',
  },

  // Redirects for better UX
  async redirects() {
    return []
  },

  // Rewrites for API routing
  async rewrites() {
    return []
  },
}

export default nextConfig
