import { useState, useCallback } from 'react'

/**
 * Custom hook for managing boolean state with toggle functionality
 */
export function useToggle(
  initialValue: boolean = false
): [boolean, () => void, (value?: boolean) => void] {
  const [value, setValue] = useState<boolean>(initialValue)

  const toggle = useCallback(() => {
    setValue(prev => !prev)
  }, [])

  const setToggle = useCallback((newValue?: boolean) => {
    setValue(newValue ?? !value)
  }, [value])

  return [value, toggle, setToggle]
}
