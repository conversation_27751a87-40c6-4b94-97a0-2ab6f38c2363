{"typescript.preferences.quoteStyle": "single", "javascript.preferences.quoteStyle": "single", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "files.associations": {"*.css": "tailwindcss"}, "editor.quickSuggestions": {"strings": true}, "tailwindCSS.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "cSpell.enabled": true, "cSpell.enableFiletypes": ["typescript", "typescriptreact", "javascript", "javascriptreact", "json", "markdown", "mdx"], "cSpell.diagnosticLevel": "Hint", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "javascript.updateImportsOnFileMove.enabled": "always", "editor.rulers": [80, 120], "editor.wordWrap": "on", "editor.minimap.enabled": false, "editor.bracketPairColorization.enabled": true, "editor.guides.bracketPairs": true, "workbench.colorTheme": "Default Dark+", "terminal.integrated.defaultProfile.windows": "PowerShell", "git.autofetch": true, "git.confirmSync": false, "explorer.confirmDelete": false, "explorer.confirmDragAndDrop": false, "files.exclude": {"**/node_modules": true, "**/.next": true, "**/dist": true, "**/build": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "search.exclude": {"**/node_modules": true, "**/.next": true, "**/dist": true, "**/build": true, "**/coverage": true, "**/.git": true}, "eslint.workingDirectories": ["."], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "prettier.requireConfig": false, "prettier.configPath": ".prettier<PERSON>", "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.wordWrap": "on"}}