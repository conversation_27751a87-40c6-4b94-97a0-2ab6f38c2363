"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Send, Paperclip, Mic } from "lucide-react"

export default function ChatSidebar() {
  const [inputMessage, setInputMessage] = useState("")

  return (
    <aside className="absolute top-0 left-0 bottom-0 w-[540px] p-3 pt-2">
      <div className="h-full bg-[#111111] rounded-xl flex flex-col overflow-hidden">
        {/* Header */}
        <div className="p-4 border-b border-gray-800">
          <div className="flex items-center justify-between">
            <div className="text-white">
              <h2 className="text-lg font-semibold">Chat</h2>
              <div className="text-xs text-gray-400 mt-1">
                AI Coding Assistant • UI Only
              </div>
            </div>
          </div>
        </div>

        {/* Messages Area */}
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4">
            <div className="text-center text-gray-500 text-sm">
              Start a conversation to begin building
              <br />
              <span className="text-xs text-gray-600">(Chat functionality disabled - UI only)</span>
            </div>
          </div>
        </ScrollArea>

        {/* Input Area */}
        <div className="border-t border-gray-800 p-6">
          <div className="relative">
            <Textarea
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              placeholder="Ask a follow-up..."
              className="resize-none bg-gray-900/50 border-gray-700 text-white placeholder-gray-400
                focus:border-gray-600 focus:ring-1 focus:ring-gray-600 transition-all duration-200
                rounded-lg px-4 py-3 pr-24 min-h-[60px] max-h-[140px] w-full"
              rows={2}
              disabled
            />
            <div className="absolute bottom-3 right-3 flex items-center space-x-2">
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0 text-gray-400 hover:text-white hover:bg-gray-800 rounded-md"
                disabled
              >
                <Paperclip className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0 text-gray-400 hover:text-white hover:bg-gray-800 rounded-md"
                disabled
              >
                <Mic className="h-4 w-4" />
              </Button>
              <Button
                type="submit"
                disabled
                className="h-7 w-7 p-0 bg-white text-black hover:bg-gray-200 disabled:bg-gray-600 disabled:text-gray-400 rounded-md"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </aside>
  )
}
