# Sophisticated Landing Page with AI Coding Assistant

A modern Next.js application featuring a sophisticated 3D globe landing page enhanced with an AI-powered coding assistant that can generate and execute code in secure sandboxes.

## 🌟 Features

### Landing Page
- **3D Interactive Globe** - Wireframe rendering with atmospheric effects
- **Modern UI** - Clean, responsive design with dark theme
- **Voice Integration** - ElevenLabs text-to-speech capabilities

### AI Coding Assistant
- **Code Generation** - Powered by OpenRouter (Claude, GPT-4, etc.)
- **Secure Execution** - E2B sandboxes for safe code running
- **Multi-Language Support** - Python, JavaScript, HTML, R, and more
- **Visual Results** - Inline charts, outputs, and error handling
- **Real-time Chat** - Seamless conversation interface

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn
- E2B API key ([get one here](https://e2b.dev))
- OpenRouter API key ([get one here](https://openrouter.ai))

### Installation

1. **Clone and install dependencies:**
   ```bash
   npm install
   ```

2. **Set up environment variables:**
   Create `.env.local` with:
   ```env
   E2B_API_KEY=your_e2b_api_key_here
   OPENROUTER_API_KEY=your_openrouter_api_key_here
   ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
   ```

3. **Run the development server:**
   ```bash
   npm run dev
   ```

4. **Open your browser:**
   Navigate to `http://localhost:3000`

## 🎯 How to Use the AI Assistant

### Basic Usage
1. **Start a conversation** - Type any coding request in the chat
2. **Get AI-generated code** - The assistant creates executable code blocks
3. **Run code safely** - Click "Run" to execute in secure sandboxes
4. **See results** - View outputs, charts, and visualizations inline
5. **Iterate and improve** - Ask for modifications and enhancements

### Example Prompts
- "Create a Python script that generates a bar chart of sales data"
- "Build a simple HTML calculator with CSS styling"
- "Implement a binary search algorithm with test cases"
- "Generate random data and perform statistical analysis"

### Supported Languages
- **Python** - Data analysis, ML, general programming
- **JavaScript/TypeScript** - Web development, algorithms
- **HTML/CSS** - Web pages and styling
- **R** - Statistical analysis
- **Bash** - System commands

## 🏗 Architecture

### Frontend
- **Next.js 15** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first styling
- **Three.js** - 3D globe rendering
- **Radix UI** - Accessible component primitives

### Backend APIs
- **OpenRouter Integration** - AI model access
- **E2B Sandbox Management** - Secure code execution
- **ElevenLabs Integration** - Text-to-speech

### Key Components
```
components/
├── ChatSidebar.tsx          # Main chat interface
├── CodeExecutionResult.tsx  # Code display and execution
├── SandboxStatus.tsx        # Sandbox status indicator
├── ExamplePrompts.tsx       # Quick start prompts
├── Globe.tsx                # 3D globe component
└── ui/                      # Reusable UI components

app/api/
├── chat/                    # OpenRouter integration
├── sandbox/create/          # E2B sandbox creation
├── sandbox/execute/         # Code execution
└── elevenlabs/              # Voice features
```

## 🔧 Configuration

### AI Model Selection
Edit `lib/config.ts` to customize:
```typescript
export const AI_CONFIG = {
  model: 'anthropic/claude-3.5-sonnet',
  temperature: 0.7,
  maxTokens: 2000
}
```

### Sandbox Settings
```typescript
export const SANDBOX_CONFIG = {
  executionTimeout: 60,
  supportedLanguages: ['python', 'javascript', 'html', 'r', 'bash']
}
```

## 🔒 Security

- **Isolated Execution** - All code runs in secure E2B sandboxes
- **No Local Access** - Code cannot access your file system
- **API Key Protection** - Environment variables for sensitive data
- **Automatic Cleanup** - Proper resource management

## 📚 Documentation

- **[E2B Integration Guide](./E2B_INTEGRATION.md)** - Detailed setup instructions
- **[Example Prompts](./EXAMPLE_PROMPTS.md)** - Ready-to-use test prompts
- **[Integration Complete](./INTEGRATION_COMPLETE.md)** - Feature overview

## 🛠 Development

### Project Structure
```
├── app/                     # Next.js App Router
│   ├── api/                 # API routes
│   ├── globals.css          # Global styles
│   ├── layout.tsx           # Root layout
│   └── page.tsx             # Home page
├── components/              # React components
├── lib/                     # Utilities and config
├── types/                   # TypeScript definitions
├── public/                  # Static assets
└── styles/                  # Additional styles
```

### Available Scripts
```bash
npm run dev      # Start development server
npm run build    # Build for production
npm run start    # Start production server
npm run lint     # Run ESLint
```

## 🚀 Deployment

### Vercel (Recommended)
1. Push to GitHub
2. Connect to Vercel
3. Add environment variables
4. Deploy automatically

### Other Platforms
- Ensure Node.js 18+ support
- Set environment variables
- Run `npm run build && npm run start`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- **E2B** - Secure code execution sandboxes
- **OpenRouter** - AI model access platform
- **ElevenLabs** - Voice synthesis technology
- **Vercel** - Hosting and deployment platform

---

**Built with ❤️ using Next.js, E2B, and OpenRouter**
