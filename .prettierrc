{"singleQuote": true, "semi": false, "tabWidth": 2, "useTabs": false, "trailingComma": "es5", "printWidth": 80, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": false, "plugins": ["@trivago/prettier-plugin-sort-imports"], "importOrder": ["^react$", "^next", "<THIRD_PARTY_MODULES>", "^@/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true}