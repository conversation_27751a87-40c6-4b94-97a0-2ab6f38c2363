#!/bin/bash

# Elite Next.js Sandbox - Optimized Development Server
# Enhanced for maximum performance and developer experience

set -e  # Exit on any error

# Environment variables for optimal performance
export NODE_ENV=development
export NEXT_TELEMETRY_DISABLED=1
export TURBOPACK=1
export NEXT_PUBLIC_APP_ENV=development

echo "🚀 Elite Next.js Sandbox - Starting optimized development server..."

# Function to check server health
function ping_server() {
	counter=0
	response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:3000")
	while [[ ${response} -ne 200 ]]; do
	  let counter++
	  if  (( counter % 20 == 0 )); then
        echo "⏳ Waiting for server to start... (attempt $counter)"
        sleep 0.1
      fi

	  response=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:3000")
	done
	echo "✅ Server is ready at http://localhost:3000"
}

# Start health check in background
ping_server &

# Change to user directory and start Next.js with Turbopack
cd /home/<USER>

echo "🔥 Starting Next.js with Turbopack for blazing fast builds..."
npx next dev --turbo --port 3000
